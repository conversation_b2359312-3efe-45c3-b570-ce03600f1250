using UnityEngine;
using PurrNet;
using Unity.Cinemachine;
using UnityEngine.InputSystem;
using Labsim;

namespace Labsim
{
    public class PhlebotomyTerminalCameraController : NetworkBehaviour , ITerminalComponent
    {
        [Header("Properties")]
        [SerializeField] private int activeCameraPriority = 15;

        [SerializeField] private bool cameraControl = true;

        [Header("Phlebotomy Specific Cameras")]
        [SerializeField] private CinemachineCamera mainTerminalCamera;
        [SerializeField] private CinemachineCamera terminalScreenCamera;
        [SerializeField] private CinemachineCamera terminalMatchCamera;
        [SerializeField] private CinemachineCamera currentCinemachineCamera;
        
        [Header("Camera Settings")]
        [SerializeField] private float lookSensitivity = 2f;
        [SerializeField] private float maxLookAngle = 80f;

      

        private float verticalRotation = 0f;
        private float horizontalRotation = 0f;


        //Dependencies

        public PlayerInputHandler playerInputHandler;

        public ITerminalServiceProvider ServiceProvider { get; set; }

        protected override void OnSpawned()
        {
            base.OnSpawned();
            InitializeListeningEvents();
        }

        protected override void OnDespawned()
        {
            UnInitializeListeningEvents();
            base.OnDespawned();
        }

        protected override void OnSpawned(bool asServer)
        {
            base.OnSpawned(asServer);
            InitializeListeningEvents();
        }

        //Listening events
        private void InitializeListeningEvents()
        {
            // Unsubscribe first to prevent multiple subscriptions
            var interactable = ServiceProvider.GetService<PhlebotomyTerminalInteractable>();
            var terminal = ServiceProvider.GetService<PhlebotomyTerminal>();
            var uiController = ServiceProvider.GetService<PhlebotomyTerminalUIController>();
            var screenInteractable = ServiceProvider.GetService<PhlebotomyScreenInteractable>();

            interactable.OnTerminalEntered -= HandleTerminalInteracted;
            interactable.OnTerminalExited -= HandleTerminalExited;
            terminal.OnPatientScanned -= HandlePatientScanned;
            uiController.OnPrintRequested -= HandlePrintRequested;
            screenInteractable.PhlebotomyScreenInteracted -= HandleScreenInteracted;

            // Subscribe
            interactable.OnTerminalEntered += HandleTerminalInteracted;
            interactable.OnTerminalExited += HandleTerminalExited;
            terminal.OnPatientScanned += HandlePatientScanned;
            uiController.OnPrintRequested += HandlePrintRequested;
            screenInteractable.PhlebotomyScreenInteracted += HandleScreenInteracted;
        }

        private void UnInitializeListeningEvents()
        {
            if (ServiceProvider != null)
            {
                var interactable = ServiceProvider.GetService<PhlebotomyTerminalInteractable>();
                var terminal = ServiceProvider.GetService<PhlebotomyTerminal>();
                var uiController = ServiceProvider.GetService<PhlebotomyTerminalUIController>();
                var screenInteractable = ServiceProvider.GetService<PhlebotomyScreenInteractable>();

                if (interactable != null) interactable.OnTerminalEntered -= HandleTerminalInteracted;
                if (interactable != null) interactable.OnTerminalExited -= HandleTerminalExited;
                if (terminal != null) terminal.OnPatientScanned -= HandlePatientScanned;
                if (uiController != null) uiController.OnPrintRequested -= HandlePrintRequested;
                if (screenInteractable != null) screenInteractable.PhlebotomyScreenInteracted -= HandleScreenInteracted;
            }
        }

        //Listening events functions
        private void HandlePrintRequested()
        {
            if (!isOwner) return; // Sadece owner client kamera kontrolü yapsın

            currentCinemachineCamera = terminalMatchCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(false);
            terminalMatchCamera.gameObject.SetActive(true);
            cameraControl = true;
            ServiceProvider.GetService<PlayerController>().PlayerCrosshair(true);
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        private void HandleTerminalInteracted(IInteractor interactor)
        {
            if (!isOwner) return; // Sadece owner client kamera kontrolü yapsın

            playerInputHandler = ServiceProvider.GetService<PlayerInputHandler>();
            mainTerminalCamera.gameObject.SetActive(true);
            currentCinemachineCamera = mainTerminalCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            verticalRotation = 6.4f;
            horizontalRotation = -85.9f;
            currentCinemachineCamera.transform.localRotation = Quaternion.Euler(6.4f, -85.9f, 0f);
        }

        private void HandleTerminalExited(IInteractor interactor)
        {
            if (!isOwner) return; // Sadece owner client kamera kontrolü yapsın

            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(false);
            terminalMatchCamera.gameObject.SetActive(false);
            currentCinemachineCamera = null;
            mainTerminalCamera.Priority = 0;
            terminalScreenCamera.Priority = 0;
            terminalMatchCamera.Priority = 0;
            cameraControl = true;
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        private void HandleScreenInteracted()
        {
            if (!isOwner) return; // Sadece owner client kamera kontrolü yapsın

            currentCinemachineCamera = terminalScreenCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(true);
            cameraControl = false;
            ServiceProvider.GetService<PlayerController>().PlayerCrosshair(false);
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }

        
        private void HandlePatientScanned(int patientId)
        {

        }

        private void OnDestroy()
        {
            UnInitializeListeningEvents();
        }

        private void Update()
        {
            if (cameraControl && currentCinemachineCamera != null && currentCinemachineCamera.Priority == activeCameraPriority)
            {
                HandleCameraRotation();
            }
        }


        private void HandleCameraRotation()
        {

            if (playerInputHandler == null || playerInputHandler.playerInput == null) return;
            Vector2 lookInput = playerInputHandler.playerInput.Terminal.TerminalLook.ReadValue<Vector2>();
            float mouseX = lookInput.x * lookSensitivity;
            float mouseY = lookInput.y * lookSensitivity;

            verticalRotation += mouseY;
            verticalRotation = Mathf.Clamp(verticalRotation, -maxLookAngle, maxLookAngle);
            
            horizontalRotation += mouseX;
            
            currentCinemachineCamera.transform.localRotation = Quaternion.Euler(verticalRotation, horizontalRotation, 0f);
        }


    }
}